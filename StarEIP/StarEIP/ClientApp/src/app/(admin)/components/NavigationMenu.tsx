"use client";

import React, { useState } from "react";
import {
  IconBabyCarriage,
  IconDashboard,
  IconLicense,
  IconFileText,
  IconUser,
  IconMail,
  IconStethoscope,
  IconReportAnalytics,
  IconAddressBook,
  IconUpload,
  IconAlertTriangle,
  IconChevronDown,
  IconHistory,
  IconClipboardList,
  IconSettings,
} from "@tabler/icons-react";
import {
  em,
  NavLink,
  Stack,
  Tooltip,
  UnstyledButton,
  useMantineTheme,
  Box,
  Collapse,
} from "@mantine/core";
import { usePathname, useRouter } from "next/navigation";
import useAuthStore, { UserPermission } from "@/app/store/AuthStore";
import { useMainStore } from "@/app/store/MainStore";
import { useMediaQuery } from "@mantine/hooks";

interface SubLink {
  label: string;
  link: string;
}

interface NavItem {
  icon: React.ReactNode;
  label: string;
  link: string;
  permission?: UserPermission;
  isExpandable?: boolean;
  links?: SubLink[];
}

const navItems: NavItem[] = [
  {
    icon: <IconDashboard size="1.5rem" stroke={1.5} />,
    label: "Dashboard",
    link: "/admin/dashboard",
    permission: "ViewDashboard",
  },
  {
    icon: <IconBabyCarriage size="1.5rem" stroke={1.5} />,
    label: "Patients",
    link: "/admin/patients",
    permission: "ViewChildren",
    isExpandable: true,
    links: [
      { label: "All Patients", link: "/admin/patients" },
      { label: "Hub Children", link: "/admin/child-detail-reports" },
      { label: "PS Children", link: "/admin/admin/ps-children" },
      { label: "Reconciliation", link: "/admin/patients/reconciliation" },
    ],
  },
  {
    icon: <IconLicense size="1.5rem" stroke={1.5} />,
    label: "My Cases",
    link: "/admin/authorizations"
  },
  {
    icon: <IconFileText size="1.5rem" stroke={1.5} />,
    label: "Faxes",
    link: "/admin/faxes",
    permission: "ViewFaxes",
  },
  {
    icon: <IconUser size="1.5rem" stroke={1.5} />,
    label: "Users",
    link: "/admin/admin/users",
    permission: "ManageUsers",
  },
  {
    icon: <IconSettings size="1.5rem" stroke={1.5} />,
    label: "Setup",
    link: "/admin/setup",
    permission: "ViewTasks",
  },
  {
    icon: <IconStethoscope size="1.5rem" stroke={1.5} />,
    label: "Physicians",
    link: "/admin/physicians",
    permission: "ViewPhysicians",
  },
  {
    icon: <IconReportAnalytics size="1.5rem" stroke={1.5} />,
    label: "Reports",
    link: "/admin/reports",
    permission: "ViewReports",
  },
  {
    icon: <IconAddressBook size="1.5rem" stroke={1.5} />,
    label: "Contacts",
    link: "/admin/contacts",
    permission: "AllowManageContacts",
  },
  {
    icon: <IconUpload size="1.5rem" stroke={1.5} />,
    label: "Data Import",
    link: "/admin/imports",
    permission: "AllowDataImport",
  },
  {
    icon: <IconAlertTriangle size="1.5rem" stroke={1.5} />,
    label: "Rejections",
    link: "/admin/rejections",
    permission: "ViewRejections",
  },
  {
    icon: <IconHistory size="1.5rem" stroke={1.5} />,
    label: "Audit Logs",
    link: "/admin/audit-logs",
    permission: "ViewAuditLogs",
  },
  {
    icon: <IconClipboardList size="1.5rem" stroke={1.5} />,
    label: "My Tasks",
    link: "/admin/mytasks",
    permission: "ViewTasks",
  },
];

const SubNavLink = ({
  label,
  link,
  active,
  onClick,
}: {
  label: string;
  link: string;
  active: boolean;
  onClick: () => void;
}) => {
  const theme = useMantineTheme();
  return (
    <NavLink
      label={label}
      component="a"
      variant="filled"
      onClick={onClick}
      active={active}
      styles={() => ({
        root: {
          padding: "10px 12px 10px 48px",
          color: "white",
          backgroundColor: active ? theme.colors.blue[8] : "transparent",
          "&:hover": {
            backgroundColor: `${theme.colors.blue[8]} !important`,
          },
          height: "40px",
          display: "flex",
          alignItems: "center",
          margin: 0,
          borderRadius: 0,
        },
        label: {
          fontSize: "14px",
        },
      })}
    />
  );
};

const NavigationMenu = () => {
  const pathname = usePathname();
  const router = useRouter();
  const theme = useMantineTheme();
  const collapsed = useMainStore((state) => state.sideMenuCollapsed);
  const setCollapsed = useMainStore((state) => state.setSideMenuCollapsed);
  const isMobile = useMediaQuery(`(max-width: ${em(750)})`);
  const [patientsMenuOpen, setPatientsMenuOpen] = useState(false);
  const permissions = useAuthStore((state) => state.permissions);
  const permissionSet = new Set(permissions);

  const handleNavigation = (link: string) => {
    if (isMobile) setCollapsed(true);
    router.push(link);
  };

  return (
    <Stack gap={0}>
      {navItems.map((item) => {
        if (item.permission && !permissionSet.has(item.permission)) return null;

        const isActive = pathname.startsWith(item.link);

        if (item.isExpandable && item.links) {
          const isAnyChildActive = item.links.some((l) =>
            pathname.startsWith(l.link),
          );

          return (
            <Box key={item.label}>
              <Tooltip
                label={item.label}
                position="right"
                disabled={!collapsed}
              >
                <Box
                  style={{
                    backgroundColor: isAnyChildActive
                      ? theme.colors.blue[7]
                      : "transparent",
                    display: "flex",
                    height: "48px",
                    width: "100%",
                  }}
                >
                  <UnstyledButton
                    onClick={() => handleNavigation(item.link)}
                    style={{
                      flex: 1,
                      height: "48px",
                      padding: "0 12px",
                      display: "flex",
                      alignItems: "center",
                      color: "white",
                    }}
                  >
                    {item.icon}
                    {!collapsed && (
                      <span style={{ marginLeft: 12, fontWeight: 500 }}>
                        {item.label}
                      </span>
                    )}
                  </UnstyledButton>
                  {!collapsed && (
                    <UnstyledButton
                      onClick={() => setPatientsMenuOpen((v) => !v)}
                      aria-expanded={patientsMenuOpen}
                      aria-controls="patients-submenu"
                      style={{
                        width: 24,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        color: "white",
                      }}
                    >
                      <IconChevronDown
                        size="0.8rem"
                        style={{
                          transform: patientsMenuOpen
                            ? "rotate(180deg)"
                            : "none",
                          transition: "transform 200ms ease",
                        }}
                      />
                    </UnstyledButton>
                  )}
                </Box>
              </Tooltip>

              {!collapsed && (
                <Collapse
                  in={patientsMenuOpen}
                  id="patients-submenu"
                  transitionDuration={0}
                >
                  <Stack
                    gap={0}
                    pl={0}
                    style={{ backgroundColor: theme.colors.blue[7] }}
                  >
                    {item.links.map((sub) => (
                      <SubNavLink
                        key={sub.link}
                        label={sub.label}
                        link={sub.link}
                        active={pathname.startsWith(sub.link)}
                        onClick={() => handleNavigation(sub.link)}
                      />
                    ))}
                  </Stack>
                </Collapse>
              )}
            </Box>
          );
        }

        return (
          <Tooltip
            key={item.label}
            label={item.label}
            position="right"
            disabled={!collapsed}
          >
            <UnstyledButton
              style={{
                width: "100%",
                height: "48px",
                backgroundColor: isActive
                  ? theme.colors.blue[7]
                  : "transparent",
              }}
              onClick={() => handleNavigation(item.link)}
            >
              <Box
                style={{
                  display: "flex",
                  alignItems: "center",
                  height: "100%",
                  padding: "0 12px",
                  color: "white",
                }}
              >
                {item.icon}
                {!collapsed && (
                  <span style={{ marginLeft: 12, fontWeight: 500 }}>
                    {item.label}
                  </span>
                )}
              </Box>
            </UnstyledButton>
          </Tooltip>
        );
      })}
    </Stack>
  );
};

export default NavigationMenu;
