'use client';

import React, { useEffect, useState } from 'react';
import { 
  Title, 
  Container, 
  Box,
  LoadingOverlay
} from '@mantine/core';
import { api } from '@/api/generated';
import { notifications } from '@mantine/notifications';
import DataGrid, {
  Column,
  Editing,
  Paging,
  Pager,
  SearchPanel,
  Toolbar,
  Item,
  Lookup,
  Form
} from 'devextreme-react/data-grid';
import { createStore } from 'devextreme-aspnet-data-nojquery';
import urlHelpers from '@/app/urlHelpers';
import { SimpleItem, GroupItem } from 'devextreme-react/form';

interface TaskTemplate {
  id: number;
  name: string;
  description: string;
  templateKey: string;
  category: string;
  subCategory?: string;
}

const categories = [
  { value: 'General', name: 'General' },
  { value: 'Patient', name: 'Patient' },
  { value: 'Authorization', name: 'Authorization' },
  { value: 'Billing', name: 'Billing' }
];

const TaskTemplateSetupPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [dataSource, setDataSource] = useState<any>(null);

  useEffect(() => {
    const serviceUrl = urlHelpers.getAbsoluteURL("api/TaskItemSetup/tasktemplates");
    
    setDataSource(createStore({
      key: "id",
      loadUrl: `${serviceUrl}`,
      insertUrl: `${serviceUrl}`,
      updateUrl: `${serviceUrl}`,
      deleteUrl: `${serviceUrl}`,
      onBeforeSend: (_, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem('jwtToken')}`,
        };
      },
      errorHandler(e) {
        console.error('Error with task template data:', e);
        notifications.show({
          title: 'Error',
          message: 'An error occurred while processing your request',
          color: 'red',
        });
      },
    }));
    
    setLoading(false);
  }, []);

  const onRowInserted = () => {
    notifications.show({
      title: 'Success',
      message: 'Task template created successfully',
      color: 'green',
    });
  };

  const onRowUpdated = () => {
    notifications.show({
      title: 'Success',
      message: 'Task template updated successfully',
      color: 'green',
    });
  };

  const onRowRemoved = () => {
    notifications.show({
      title: 'Success',
      message: 'Task template deleted successfully',
      color: 'green',
    });
  };

  return (
    <Container fluid p="md">
      <Box pos="relative">
        <LoadingOverlay visible={loading} />
        <Title order={1} mb="xl">Task Template Setup</Title>
        
        <DataGrid
          dataSource={dataSource}
          showBorders={true}
          columnAutoWidth={true}
          wordWrapEnabled={true}
          height="calc(100vh - 200px)"
          onRowInserted={onRowInserted}
          onRowUpdated={onRowUpdated}
          onRowRemoved={onRowRemoved}
        >
          <SearchPanel visible={true} width={240} placeholder="Search..." />
          <Editing
            mode="popup"
            allowAdding={true}
            allowUpdating={true}
            allowDeleting={true}
            useIcons={true}
          >
            <Form>
              <GroupItem colCount={2}>
                <SimpleItem dataField="name" />
                <SimpleItem dataField="templateKey" />
                <SimpleItem dataField="category" editorType="dxSelectBox" />
                <SimpleItem dataField="subCategory" />
                <SimpleItem dataField="description" editorType="dxTextArea" colSpan={2} editorOptions={{ height: 100 }} />
              </GroupItem>
            </Form>
          </Editing>
          
          <Column dataField="id" caption="ID" allowEditing={false} visible={false} />
          <Column 
            dataField="name" 
            caption="Template Name" 
            validationRules={[{ type: 'required' }]}
          />
          <Column 
            dataField="templateKey" 
            caption="Template Key" 
            validationRules={[{ type: 'required' }]}
          />
          <Column 
            dataField="category" 
            caption="Category" 
            validationRules={[{ type: 'required' }]}
          >
            <Lookup 
              dataSource={categories} 
              valueExpr="value" 
              displayExpr="name" 
            />
          </Column>
          <Column 
            dataField="subCategory" 
            caption="Sub-Category" 
          />
          <Column 
            dataField="description" 
            caption="Description" 
            cellTemplate="descriptionTemplate"
          />
          
          <Paging defaultPageSize={20} />
          <Pager
            showPageSizeSelector={true}
            allowedPageSizes={[10, 20, 50]}
            showInfo={true}
          />
          <Toolbar>
            <Item name="addRowButton" />
            <Item name="searchPanel" />
          </Toolbar>
        </DataGrid>
      </Box>
    </Container>
  );
};

export default TaskTemplateSetupPage;

