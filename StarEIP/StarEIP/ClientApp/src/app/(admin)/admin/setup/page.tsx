'use client';

import React from 'react';
import {
  Title,
  Container,
  Text,
  Stack,
  Paper,
  Anchor,
  Grid,
  Divider
} from '@mantine/core';
import Link from 'next/link';
import { useHasPermission } from '@/app/store/AuthStore';

interface SetupLinkProps {
  title: string;
  description?: string;
  link: string;
  permission?: string;
}

interface SetupCategoryProps {
  title: string;
  links: SetupLinkProps[];
}

const SetupLink: React.FC<SetupLinkProps> = ({ title, description, link, permission }) => {
  const hasPermission = useHasPermission(permission || '');

  if (permission && !hasPermission) {
    return null;
  }

  return (
    <Link href={link} passHref legacyBehavior>
      <Anchor
        component="a"
        size="sm"
        style={{
          display: 'block',
          padding: '8px 12px',
          borderRadius: '4px',
          textDecoration: 'none',
          color: 'inherit',
          transition: 'background-color 0.2s ease'
        }}
        sx={(theme) => ({
          '&:hover': {
            backgroundColor: theme.colors.gray[0],
            textDecoration: 'none'
          }
        })}
      >
        <Text fw={500} size="sm">{title}</Text>
        {description && (
          <Text c="dimmed" size="xs" mt={2}>{description}</Text>
        )}
      </Anchor>
    </Link>
  );
};

const SetupCategory: React.FC<SetupCategoryProps> = ({ title, links }) => {
  const visibleLinks = links.filter(link => {
    if (!link.permission) return true;
    return useHasPermission(link.permission);
  });

  if (visibleLinks.length === 0) return null;

  return (
    <Paper p="md" withBorder>
      <Text fw={600} size="md" mb="sm" c="blue.7">
        {title}
      </Text>
      <Stack gap="xs">
        {visibleLinks.map((link, index) => (
          <SetupLink key={index} {...link} />
        ))}
      </Stack>
    </Paper>
  );
};

const SetupPage: React.FC = () => {
  const setupCategories: SetupCategoryProps[] = [
    {
      title: 'Task Management',
      links: [
        {
          title: 'Task Status Setup',
          description: 'Configure task statuses for workflow management',
          link: '/admin/setup/task-status',
          permission: 'ViewTasks'
        },
        {
          title: 'Task Template Setup',
          description: 'Manage task templates for standardized workflows',
          link: '/admin/setup/task-templates',
          permission: 'ViewTasks'
        }
      ]
    },
    {
      title: 'Communication',
      links: [
        {
          title: 'Email Templates Setup',
          description: 'Configure email templates for automated communications',
          link: '/admin/admin/emailTemplates',
          permission: 'ViewEmailTemplates'
        }
      ]
    },
    {
      title: 'Reporting',
      links: [
        {
          title: 'Reports Setup',
          description: 'Manage and configure system reports',
          link: '/admin/reports',
          permission: 'ViewReports'
        }
      ]
    },
    {
      title: 'User Management',
      links: [
        {
          title: 'User Management',
          description: 'Manage system users and permissions',
          link: '/admin/admin/users',
          permission: 'ManageUsers'
        }
      ]
    },
    {
      title: 'Data Management',
      links: [
        {
          title: 'Data Import',
          description: 'Import data from external sources',
          link: '/admin/imports',
          permission: 'AllowDataImport'
        },
        {
          title: 'Contact Management',
          description: 'Manage system contacts',
          link: '/admin/contacts',
          permission: 'AllowManageContacts'
        }
      ]
    },
    {
      title: 'System Monitoring',
      links: [
        {
          title: 'Audit Logs',
          description: 'View system audit logs and activity',
          link: '/admin/audit-logs',
          permission: 'ViewAuditLogs'
        },
        {
          title: 'Rejections',
          description: 'View and manage system rejections',
          link: '/admin/rejections',
          permission: 'ViewRejections'
        }
      ]
    }
  ];

  return (
    <Container fluid p="md">
      <Title order={1} mb="xl">System Setup</Title>
      <Text c="dimmed" mb="xl">
        Configure and manage various aspects of the system. Select a category below to access specific setup options.
      </Text>

      <Grid>
        {setupCategories.map((category, index) => (
          <Grid.Col key={index} span={{ base: 12, sm: 6, lg: 4 }}>
            <SetupCategory {...category} />
          </Grid.Col>
        ))}
      </Grid>
    </Container>
  );
};

export default SetupPage;

