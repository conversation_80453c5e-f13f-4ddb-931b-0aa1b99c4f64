'use client';

import React, { useEffect, useState, useRef } from 'react';
import DataGrid, {
  Column,
  Editing,
  Paging,
  Pager,
  SearchPanel,
  Toolbar,
  Item
} from 'devextreme-react/data-grid';
import { createStore, CustomStore } from 'devextreme-aspnet-data-nojquery';
import Button from 'devextreme-react/button';
import urlHelpers from '@/app/urlHelpers';
import { showNotification } from '@/app/Utils/notificationUtils';

const TaskStatusSetupPage: React.FC = () => {
  const [dataSource, setDataSource] = useState<CustomStore | null>(null);
  const dataGridRef = useRef<any>(null);

  const loadDataSource = () => {
    const serviceUrl = urlHelpers.getAbsoluteURL("api/TaskItemSetup/taskstatuses");

    setDataSource(createStore({
      key: "id",
      loadUrl: serviceUrl,
      insertUrl: serviceUrl,
      updateUrl: serviceUrl,
      deleteUrl: serviceUrl,
      onBeforeSend: (_, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem('jwtToken')}`,
        };
      },
      errorHandler(e) {
        console.error('Error with task status data:', e);
        showNotification('error', 'An error occurred while processing your request');
      },
    }));
  };

  useEffect(() => {
    loadDataSource();
  }, []);

  return (
    <DataGrid
      ref={dataGridRef}
      remoteOperations
      dataSource={dataSource}
      height="100%"
      width="100%"
      allowColumnReordering={true}
      allowColumnResizing={true}
      columnAutoWidth={true}
      showBorders={true}
      columnResizingMode={"widget"}
      showColumnLines
      twoWayBindingEnabled
      rowAlternationEnabled
      focusedRowEnabled
      autoNavigateToFocusedRow
    >
      <SearchPanel visible={true} width={240} placeholder="Search..." />
      <Editing
        mode="row"
        allowAdding={true}
        allowUpdating={true}
        allowDeleting={true}
        useIcons={true}
      />
      <Column dataField="id" caption="ID" allowEditing={false} visible={false} />
      <Column
        dataField="name"
        caption="Status Name"
        validationRules={[{ type: 'required' }]}
      />

      <Paging defaultPageSize={20} />
      <Pager
        showPageSizeSelector={true}
        allowedPageSizes={[10, 20, 50]}
        showInfo={true}
      />
      <Toolbar>
        <Item name="addRowButton" />
        <Item location="before">
          <Button icon="refresh" onClick={loadDataSource} />
        </Item>
        <Item name="searchPanel" />
      </Toolbar>
    </DataGrid>
  );
};

export default TaskStatusSetupPage;