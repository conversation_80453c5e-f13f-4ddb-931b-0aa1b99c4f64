'use client';

import React, { useEffect, useState } from 'react';
import { 
  Title, 
  Container, 
  Box,
  LoadingOverlay
} from '@mantine/core';
import { api } from '@/api/generated';
import { notifications } from '@mantine/notifications';
import DataGrid, {
  Column,
  Editing,
  Paging,
  Pager,
  SearchPanel,
  Toolbar,
  Item
} from 'devextreme-react/data-grid';
import { createStore } from 'devextreme-aspnet-data-nojquery';
import urlHelpers from '@/app/urlHelpers';

interface TaskStatus {
  id: number;
  name: string;
}

const TaskStatusSetupPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [dataSource, setDataSource] = useState<any>(null);

  useEffect(() => {
    const serviceUrl = urlHelpers.getAbsoluteURL("api/TaskItemSetup/taskstatuses");
    
    setDataSource(createStore({
      key: "id",
      loadUrl: `${serviceUrl}`,
      insertUrl: `${serviceUrl}`,
      updateUrl: `${serviceUrl}`,
      deleteUrl: `${serviceUrl}`,
      onBeforeSend: (_, s) => {
        s.headers = {
          Authorization: `Bearer ${localStorage.getItem('jwtToken')}`,
        };
      },
      errorHandler(e) {
        console.error('Error with task status data:', e);
        notifications.show({
          title: 'Error',
          message: 'An error occurred while processing your request',
          color: 'red',
        });
      },
    }));
    
    setLoading(false);
  }, []);

  const onRowInserted = () => {
    notifications.show({
      title: 'Success',
      message: 'Task status created successfully',
      color: 'green',
    });
  };

  const onRowUpdated = () => {
    notifications.show({
      title: 'Success',
      message: 'Task status updated successfully',
      color: 'green',
    });
  };

  const onRowRemoved = () => {
    notifications.show({
      title: 'Success',
      message: 'Task status deleted successfully',
      color: 'green',
    });
  };

  return (
    <Container fluid p="md">
      <Box pos="relative">
        <LoadingOverlay visible={loading} />
        <Title order={1} mb="xl">Task Status Setup</Title>
        
        <DataGrid
          dataSource={dataSource}
          showBorders={true}
          columnAutoWidth={true}
          wordWrapEnabled={true}
          height="calc(100vh - 200px)"
          onRowInserted={onRowInserted}
          onRowUpdated={onRowUpdated}
          onRowRemoved={onRowRemoved}
        >
          <SearchPanel visible={true} width={240} placeholder="Search..." />
          <Editing
            mode="row"
            allowAdding={true}
            allowUpdating={true}
            allowDeleting={true}
            useIcons={true}
          />
          <Column dataField="id" caption="ID" allowEditing={false} visible={false} />
          <Column 
            dataField="name" 
            caption="Status Name" 
            validationRules={[{ type: 'required' }]}
          />
          
          <Paging defaultPageSize={20} />
          <Pager
            showPageSizeSelector={true}
            allowedPageSizes={[10, 20, 50]}
            showInfo={true}
          />
          <Toolbar>
            <Item name="addRowButton" />
            <Item name="searchPanel" />
          </Toolbar>
        </DataGrid>
      </Box>
    </Container>
  );
};

export default TaskStatusSetupPage;

